import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { User, UserDocument, ApprovalStatus } from './schemas/user.schema';
import { OtpService } from '../otp/otp.service';
import { AuthService } from '../auth/auth.service';
import { SendOtpDto } from './dto/send-otp.dto';
import { VerifyOtpDto } from './dto/verify-otp.dto';
import { UpdateUserProfileDto } from './dto/update-user-profile.dto';

@Injectable()
export class UsersService {
  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    private otpService: OtpService,
    private authService: AuthService,
  ) {}

  /**
   * Normalize Iranian mobile number to consistent format
   */
  private normalizeMobileNumber(mobileNumber: string): string {
    // Remove spaces and convert to string
    let normalized = mobileNumber.replace(/\s/g, '');

    // Convert +98 format to 09 format
    if (normalized.startsWith('+98')) {
      normalized = '0' + normalized.substring(3);
    }

    return normalized;
  }

  /**
   * Check if user profile is complete with all required data
   */
  private isProfileComplete(user: UserDocument): boolean {
    return !!(
      user.name &&
      user.familyName &&
      user.nationalCode &&
      user.state &&
      user.city &&
      user.address &&
      user.nationalCardImagePath &&
      user.authImagePath
    );
  }

  /**
   * Find user by mobile number
   */
  async findByMobileNumber(mobileNumber: string): Promise<UserDocument | null> {
    const normalizedMobile = this.normalizeMobileNumber(mobileNumber);
    return this.userModel.findOne({ mobileNumber: normalizedMobile }).exec();
  }

  /**
   * Create a new user
   */
  async createUser(mobileNumber: string, email?: string): Promise<UserDocument> {
    const normalizedMobile = this.normalizeMobileNumber(mobileNumber);

    // Check if mobile number already exists
    const existingMobile = await this.userModel.findOne({ mobileNumber: normalizedMobile });
    if (existingMobile) {
      throw new ConflictException('شماره موبایل قبلاً ثبت شده است');
    }

    // Check if email already exists
    const existingEmail = await this.userModel.findOne({ email });
    if (existingEmail) {
      throw new ConflictException('ایمیل قبلاً ثبت شده است');
    }

    const user = new this.userModel({ mobileNumber: normalizedMobile, email });
    return user.save();
  }

  /**
   * Send OTP to user's mobile number
   * If user doesn't exist, create them first
   */
  async sendOtp(sendOtpDto: SendOtpDto): Promise<{
    message: string;
    code: string;
    expiresAt: Date;
    isNewUser: boolean;
  }> {
    const { mobileNumber } = sendOtpDto;

    // Check if user exists by mobile number
    let user: UserDocument | null = await this.userModel.findOne({
      mobileNumber: this.normalizeMobileNumber(mobileNumber)
    }).exec();
    let isNewUser = false;

    // If user doesn't exist, create them
    if (!user) {
      user = await this.createUser(mobileNumber);
      isNewUser = true;
    }

    // Generate and send OTP
    const { code, expiresAt } = await this.otpService.createOtp(mobileNumber);

    return {
      message: isNewUser
        ? 'User registered successfully. OTP sent to your mobile number.'
        : 'OTP sent to your mobile number.',
      code, // In production, this should be sent via SMS, not returned in response
      expiresAt,
      isNewUser,
    };
  }

  /**
   * Verify OTP and authenticate user
   */
  async verifyOtp(verifyOtpDto: VerifyOtpDto): Promise<{
    message: string;
    user: UserDocument;
    isValid: boolean;
    accessToken?: string;
  }> {
    const { mobileNumber, code } = verifyOtpDto;

    // Find user
    const user = await this.findByMobileNumber(mobileNumber);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Verify OTP
    const isValid = await this.otpService.verifyOtp(mobileNumber, code);

    let accessToken: string | undefined;

    if (isValid) {
      // Update last login time
      user.lastLoginAt = new Date();
      await user.save();

      // Generate JWT access token
      accessToken = await this.authService.generateAccessToken(user);
    }

    return {
      message: isValid ? 'OTP verified successfully' : 'Invalid or expired OTP',
      user,
      isValid,
      accessToken,
    };
  }

  /**
   * Get all users (for admin purposes)
   */
  async findAll(): Promise<UserDocument[]> {
    return this.userModel.find().exec();
  }

  /**
   * Get user by ID
   */
  async findOne(id: string): Promise<UserDocument> {
    const user = await this.userModel.findById(id).exec();
    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }
    return user;
  }

  /**
   * Update user profile with personal data and documents
   */
  async updateUserProfile(
    userId: string,
    updateData: UpdateUserProfileDto,
    files?: {
      nationalCardImage?: Express.Multer.File[];
      authImage?: Express.Multer.File[];
    }
  ): Promise<UserDocument> {
    // Find the user
    const user = await this.userModel.findById(userId).exec();
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Check if national code is already taken by another user
    if (updateData.nationalCode) {
      const existingUser = await this.userModel.findOne({
        nationalCode: updateData.nationalCode,
        _id: { $ne: userId }
      }).exec();

      if (existingUser) {
        throw new ConflictException('National code is already registered by another user');
      }
    }

    // Update user data
    if (updateData.name) user.name = updateData.name;
    if (updateData.familyName) user.familyName = updateData.familyName;
    if (updateData.nationalCode) user.nationalCode = updateData.nationalCode;
    if (updateData.state) user.state = updateData.state;
    if (updateData.city) user.city = updateData.city;
    if (updateData.address) user.address = updateData.address;

     const makeRelative = (absPath: string) => {
      // Find the index of 'uploads/user-documents' in the absolute path
      const uploadsIndex = absPath.indexOf('uploads/user-documents');
      if (uploadsIndex !== -1) {
        return absPath.substring(uploadsIndex);
      }
      return absPath; // fallback to original if not found
    };

    // Handle file uploads
    if (files?.nationalCardImage?.[0]) {
      user.nationalCardImagePath = makeRelative(files.nationalCardImage[0].path);
    }
    if (files?.authImage?.[0]) {
      user.authImagePath = makeRelative(files.authImage[0].path);
    }

    // Check if profile is complete and set appropriate status
    if (this.isProfileComplete(user)) {
      user.approvalStatus = ApprovalStatus.PENDING;
      user.submittedAt = new Date();
    } else {
      // Keep current status if profile is incomplete, but don't set submittedAt
      if (user.approvalStatus === ApprovalStatus.NOT_SUBMITTED) {
        user.approvalStatus = ApprovalStatus.NOT_SUBMITTED;
      }
    }

    return user.save();
  }
}
